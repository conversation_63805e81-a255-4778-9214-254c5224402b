import React from 'react';
import { SimpleNode, VisualConnection } from './types';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';

interface ContextualInfoPanelProps {
  selectedNode: SimpleNode | null;
  selectedEdge: VisualConnection | null;
  position: { x: number; y: number };
  onClose: () => void;
}

export const ContextualInfoPanel: React.FC<ContextualInfoPanelProps> = ({
  selectedNode,
  selectedEdge,
  position,
  onClose
}) => {
  if (!selectedNode && !selectedEdge) return null;
  const panelStyle: React.CSSProperties = {
    position: 'absolute',
    left: position.x + 20,
    top: position.y,
    zIndex: 15,
    maxWidth: '300px',
    backgroundColor: 'rgba(20, 20, 40, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '8px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5)',
  };

  const renderNodeInfo = (node: SimpleNode) => (
    <Card style={panelStyle}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-white text-sm">Node Details</CardTitle>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white text-lg leading-none"
            style={{ background: 'none', border: 'none', cursor: 'pointer' }}
          >
            ×
          </button>
        </div>
      </CardHeader>
      <CardContent className="text-white text-xs space-y-2">
        <div>
          <strong className="text-blue-300">ID:</strong> 
          <span className="ml-2">{node.id}</span>
        </div>
        
        {node.data.label && (
          <div>
            <strong className="text-blue-300">Label:</strong> 
            <span className="ml-2">{node.data.label}</span>
          </div>
        )}
        
        {node.data.value !== undefined && (
          <div>
            <strong className="text-blue-300">Value:</strong> 
            <span className="ml-2">{node.data.value}</span>
          </div>
        )}
        
        {node.data.category && (
          <div>
            <strong className="text-blue-300">Category:</strong> 
            <Badge variant="secondary" className="ml-2 text-xs">
              {node.data.category}
            </Badge>
          </div>
        )}
        
        <div>
          <strong className="text-blue-300">Position:</strong> 
          <span className="ml-2">
            ({node.position.x.toFixed(2)}, {node.position.y.toFixed(2)})
          </span>
        </div>
        
        {node.data.tags && node.data.tags.length > 0 && (
          <>
            <Separator className="my-2 bg-gray-600" />
            <div>
              <strong className="text-blue-300">Tags:</strong>
              <div className="flex flex-wrap gap-1 mt-1">
                {node.data.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );

  const renderEdgeInfo = (edge: VisualConnection) => (
    <Card style={panelStyle}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-white text-sm">Edge Details</CardTitle>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white text-lg leading-none"
            style={{ background: 'none', border: 'none', cursor: 'pointer' }}
          >
            ×
          </button>
        </div>
      </CardHeader>
      <CardContent className="text-white text-xs space-y-2">
        <div>
          <strong className="text-blue-300">ID:</strong> 
          <span className="ml-2">{edge.id}</span>
        </div>
        
        <div>
          <strong className="text-blue-300">Connection:</strong> 
          <div className="ml-2 mt-1">
            <div>{edge.sourceNode.data.label || edge.sourceNode.id}</div>
            <div className="text-gray-400">↓</div>
            <div>{edge.targetNode.data.label || edge.targetNode.id}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return selectedNode ? renderNodeInfo(selectedNode) : selectedEdge ? renderEdgeInfo(selectedEdge) : null;
};
