
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";
import { QuestionContext } from "@/components/conversation-planner/QuestionContextSelector";
import { formatInlineContent } from "./ContentFormatter";
import { Checkbox } from "@/components/ui/checkbox";

// Discriminated union for better type safety
type FollowUpSectionProps = {
  questionContext: QuestionContext;
  followUpQuestions: string[];
  onFollowUpQuestion: (question: string) => void;
} & (
  | {
      // When selection is enabled, both props are required
      selectionEnabled: true;
      onItemToggleSelect: (question: string) => void;
      isItemSelected: (question: string) => boolean;
    }
  | {
      // When selection is disabled, props are not needed
      selectionEnabled?: false;
      onItemToggleSelect?: never;
      isItemSelected?: never;
    }
);

export const FollowUpSection: React.FC<FollowUpSectionProps> = ({
  questionContext,
  followUpQuestions,
  onFollowUpQuestion,
  selectionEnabled = false,
  onItemToggleSelect,
  isItemSelected,
}) => {
  // Only show the section if we have actual follow-up questions
  if (!followUpQuestions || followUpQuestions.length === 0) {
    return null;
  }

  const getFollowUpTitle = () => {
    return questionContext === "asking"
      ? "Keep the Conversation Going"
      : "Redirect or Continue the Conversation";
  };

  const getFollowUpDescription = () => {
    return questionContext === "asking"
      ? "Use these AI-generated follow-up questions to continue the conversation:"
      : "Use these AI-generated questions to redirect or learn more about their intent:";
  };

  return (
    <div className="bg-primary/10 border border-primary/20 p-6 rounded-lg">
      <div className="flex items-center mb-4">
        <MessageSquare className="h-5 w-5 mr-3 text-primary" />
        <h4 className="text-lg font-semibold text-foreground">
          {getFollowUpTitle()}
        </h4>
      </div>
      <p className="text-sm text-muted-foreground mb-4">
        {getFollowUpDescription()}
      </p>      <div className="space-y-3">
        {followUpQuestions.map((question, index) => (
          <div key={index} className="flex items-center gap-3">
            {selectionEnabled && onItemToggleSelect && isItemSelected && (
              <Checkbox
                checked={isItemSelected(question)}
                onCheckedChange={() => onItemToggleSelect(question)}
              />
            )}
            <Button
              variant="outline"
              onClick={() => onFollowUpQuestion(question)}
              className="flex-1 text-left p-4 h-auto bg-card/80 border-border hover:bg-muted justify-start group transition-all duration-200"
            >
              <div className="flex items-start w-full">
                <div className="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0 mr-3 mt-0.5 group-hover:bg-primary/30">
                  <span className="text-primary font-semibold text-xs">{index + 1}</span>
                </div>
                <span
                  className="text-sm text-foreground/80 text-left break-words whitespace-normal leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: formatInlineContent(question) }}
                />
              </div>
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
