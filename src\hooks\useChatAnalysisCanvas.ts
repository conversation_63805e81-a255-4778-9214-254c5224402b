import { useState, useEffect, useCallback } from 'react';
import { AnalysisResult } from '@/types/conversation';
import { ChatAnalysisCanvasData, AnalysisCluster, ChatSimulation } from '@/components/visual-analysis/types';
import { ChatAnalysisCanvasService } from '@/services/visual-analysis/chatAnalysisCanvasService';
import { useAnalysisStore } from '@/stores/useAnalysisStore';
import { useLibraryStore } from '@/stores/useLibraryStore';

/**
 * Hook for managing chat analysis canvas data integration
 */
export const useChatAnalysisCanvas = () => {
  const [canvasData, setCanvasData] = useState<ChatAnalysisCanvasData | null>(null);
  const [clusters, setClusters] = useState<AnalysisCluster[]>([]);
  const [simulations, setSimulations] = useState<ChatSimulation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get analysis results from stores
  const { analysisResults } = useAnalysisStore();
  const { savedAnalyses } = useLibraryStore();

  /**
   * Load canvas data from analysis results
   */
  const loadCanvasData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Combine current analysis results with saved analyses
      const allAnalysisResults: AnalysisResult[] = [
        ...analysisResults,
        ...savedAnalyses.flatMap(saved => saved.results)
      ];

      if (allAnalysisResults.length === 0) {
        setCanvasData({
          nodes: [],
          connections: [],
          clusters: [],
          simulations: [],
          metadata: {
            lastUpdated: new Date(),
            version: '1.0.0'
          }
        });
        return;
      }

      // Generate connections based on analysis similarity
      const connections = ChatAnalysisCanvasService.generateConnectionsFromAnalysisResults(allAnalysisResults);

      // Convert to canvas data
      const newCanvasData = ChatAnalysisCanvasService.analysisResultsToCanvasData(
        allAnalysisResults,
        connections,
        clusters
      );

      // Add simulations
      newCanvasData.simulations = simulations;

      setCanvasData(newCanvasData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load canvas data');
      console.error('Error loading canvas data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [analysisResults, savedAnalyses, clusters, simulations]);

  /**
   * Create a new cluster from selected node IDs
   */
  const createCluster = useCallback((nodeIds: string[], name: string, description?: string) => {
    const newCluster: AnalysisCluster = {
      id: `cluster_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name,
      description,
      nodeIds,
      color: Math.floor(Math.random() * 0xffffff), // Random color
      createdAt: new Date(),
      updatedAt: new Date(),
      tags: ['user-created']
    };

    setClusters(prev => [...prev, newCluster]);
    
    // Update canvas data to reflect cluster changes
    if (canvasData) {
      const updatedCanvasData = {
        ...canvasData,
        clusters: [...clusters, newCluster],
        nodes: canvasData.nodes.map(node => 
          nodeIds.includes(node.id) 
            ? { ...node, clusterId: newCluster.id }
            : node
        )
      };
      setCanvasData(updatedCanvasData);
    }

    return newCluster;
  }, [clusters, canvasData]);

  /**
   * Update an existing cluster
   */
  const updateCluster = useCallback((clusterId: string, updates: Partial<AnalysisCluster>) => {
    setClusters(prev => prev.map(cluster => 
      cluster.id === clusterId 
        ? { ...cluster, ...updates, updatedAt: new Date() }
        : cluster
    ));

    // Update canvas data
    if (canvasData) {
      const updatedCanvasData = {
        ...canvasData,
        clusters: clusters.map(cluster => 
          cluster.id === clusterId 
            ? { ...cluster, ...updates, updatedAt: new Date() }
            : cluster
        )
      };
      setCanvasData(updatedCanvasData);
    }
  }, [clusters, canvasData]);

  /**
   * Delete a cluster
   */
  const deleteCluster = useCallback((clusterId: string) => {
    setClusters(prev => prev.filter(cluster => cluster.id !== clusterId));

    // Update canvas data to remove cluster references
    if (canvasData) {
      const updatedCanvasData = {
        ...canvasData,
        clusters: clusters.filter(cluster => cluster.id !== clusterId),
        nodes: canvasData.nodes.map(node => 
          node.clusterId === clusterId 
            ? { ...node, clusterId: undefined }
            : node
        )
      };
      setCanvasData(updatedCanvasData);
    }
  }, [clusters, canvasData]);

  /**
   * Create a new simulation
   */
  const createSimulation = useCallback((
    name: string, 
    description: string, 
    sourceNodeIds: string[], 
    prompts: any[]
  ) => {
    const newSimulation: ChatSimulation = {
      id: `simulation_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name,
      description,
      sourceNodeIds,
      prompts,
      results: [],
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setSimulations(prev => [...prev, newSimulation]);
    
    // Update canvas data
    if (canvasData) {
      const updatedCanvasData = {
        ...canvasData,
        simulations: [...simulations, newSimulation]
      };
      setCanvasData(updatedCanvasData);
    }

    return newSimulation;
  }, [simulations, canvasData]);

  /**
   * Update simulation status and results
   */
  const updateSimulation = useCallback((simulationId: string, updates: Partial<ChatSimulation>) => {
    setSimulations(prev => prev.map(sim => 
      sim.id === simulationId 
        ? { ...sim, ...updates, updatedAt: new Date() }
        : sim
    ));

    // Update canvas data
    if (canvasData) {
      const updatedCanvasData = {
        ...canvasData,
        simulations: simulations.map(sim => 
          sim.id === simulationId 
            ? { ...sim, ...updates, updatedAt: new Date() }
            : sim
        )
      };
      setCanvasData(updatedCanvasData);
    }
  }, [simulations, canvasData]);

  /**
   * Get analysis record by node ID
   */
  const getAnalysisRecordByNodeId = useCallback((nodeId: string): AnalysisResult | null => {
    if (!canvasData) return null;
    
    const node = canvasData.nodes.find(n => n.id === nodeId);
    return node?.analysisRecord || null;
  }, [canvasData]);

  /**
   * Get cluster by ID
   */
  const getClusterById = useCallback((clusterId: string): AnalysisCluster | null => {
    return clusters.find(cluster => cluster.id === clusterId) || null;
  }, [clusters]);

  /**
   * Get nodes in a cluster
   */
  const getNodesInCluster = useCallback((clusterId: string) => {
    if (!canvasData) return [];
    return canvasData.nodes.filter(node => node.clusterId === clusterId);
  }, [canvasData]);

  // Load canvas data when dependencies change
  useEffect(() => {
    loadCanvasData();
  }, [loadCanvasData]);

  return {
    canvasData,
    clusters,
    simulations,
    isLoading,
    error,
    loadCanvasData,
    createCluster,
    updateCluster,
    deleteCluster,
    createSimulation,
    updateSimulation,
    getAnalysisRecordByNodeId,
    getClusterById,
    getNodesInCluster
  };
};
