import * as THREE from 'three';

// Represents the raw data for a node
export interface NodeData {
  id: string;
  label?: string;
  value?: number; // Example data property
  category?: string; // Example data property
  // position is now primarily 2D, z can be used for layering if needed
  position?: { x: number; y: number; z?: number }; 
  color?: THREE.ColorRepresentation;
  // mass is no longer directly used by physics, but can be a data property
  mass?: number; 
  tags?: string[]; // Added for analysis
  clusterId?: string; // Added for clustering
}

// Represents the raw data for a connection/edge
export interface ConnectionData {
  id: string;
  source: string; // ID of the source node
  target: string; // ID of the target node
  // strength is now conceptual for layout, not physics spring stiffness
  strength?: number; // Example: for spring stiffness
  label?: string; // Added for analysis
  tags?: string[]; // Added for analysis
}

// Combined data for the canvas
export interface CanvasData {
  nodes: NodeData[];
  connections: ConnectionData[];
}

// Simplified Node representation for 2D (used internally by LivingDataCanvas)
// This replaces PhysicsNode
export interface SimpleNode {
  id: string;
  mesh: THREE.Mesh | THREE.Sprite; 
  position: THREE.Vector2; // Current 2D position for layout and rendering
  data: NodeData; // Original data
  setHighlight: (highlight: boolean) => void;
  dispose: () => void;
  // Properties for force-directed layout
  force?: THREE.Vector2; 
  velocity?: THREE.Vector2;
  isDragged?: boolean; // Added for layout interaction
}

// Visual representation of connections (mostly unchanged, but source/targetNode types will be SimpleNode)
export interface VisualConnection {
  id: string;
  lineMesh: THREE.LineSegments | THREE.Line; // THREE.Line for Line2
  sourceNode: SimpleNode; // CHANGED from PhysicsNode
  targetNode: SimpleNode; // CHANGED from PhysicsNode
  update: () => void; 
  dispose: () => void;
}

// PhysicsSpring is removed
// export interface PhysicsSpring { ... }
